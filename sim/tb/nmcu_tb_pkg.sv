// File: sim/tb/nmcu_tb_pkg.sv
// NMCU Chiplet Testbench Package - UVM Components and Types

package nmcu_tb_pkg;
  
  import uvm_pkg::*;
  `include "uvm_macros.svh"
  
  // Import design packages
  // import nmcu_pkg::*;
  
  // Test configuration parameters
  parameter int MATRIX_SIZE = 64;
  parameter int PE_ARRAY_SIZE = 8;
  parameter int CACHE_SIZE = 1024;
  parameter int DATA_WIDTH = 32;
  parameter int ADDR_WIDTH = 32;
  parameter int UCIE_CHANNELS = 8;
  
  // Transaction types
  typedef enum {
    MATRIX_MUL,
    CONV_OP,
    VECTOR_ADD,
    CACHE_FLUSH,
    NOP
  } operation_type_e;
  
  typedef enum {
    READ_REQ,
    WRITE_REQ,
    CTRL_CMD
  } req_type_e;
  
  //=============================================================================
  // Transaction Classes
  //=============================================================================
  
  // Base transaction for memory operations
  class mem_transaction extends uvm_sequence_item;
    rand bit [ADDR_WIDTH-1:0] addr;
    rand bit [DATA_WIDTH-1:0] data;
    rand req_type_e req_type;
    rand int burst_length;
    bit [DATA_WIDTH-1:0] response_data;
    bit response_valid;
    
    `uvm_object_utils_begin(mem_transaction)
      `uvm_field_int(addr, UVM_ALL_ON)
      `uvm_field_int(data, UVM_ALL_ON)
      `uvm_field_enum(req_type_e, req_type, UVM_ALL_ON)
      `uvm_field_int(burst_length, UVM_ALL_ON)
    `uvm_object_utils_end
    
    constraint valid_addr_c { addr inside {[0:32'hFFFF_FFFF]}; }
    constraint burst_len_c { burst_length inside {[1:16]}; }
    
    function new(string name = "mem_transaction");
      super.new(name);
    endfunction
  endclass
  
  // UCIe transaction for chiplet communication
  class ucie_transaction extends uvm_sequence_item;
    rand bit [7:0] channel_id;
    rand bit [DATA_WIDTH-1:0] payload;
    rand bit [ADDR_WIDTH-1:0] dest_addr;
    rand bit valid;
    bit ready;
    bit ack;
    
    `uvm_object_utils_begin(ucie_transaction)
      `uvm_field_int(channel_id, UVM_ALL_ON)
      `uvm_field_int(payload, UVM_ALL_ON)
      `uvm_field_int(dest_addr, UVM_ALL_ON)
      `uvm_field_int(valid, UVM_ALL_ON)
    `uvm_object_utils_end
    
    constraint valid_channel_c { channel_id < UCIE_CHANNELS; }
    
    function new(string name = "ucie_transaction");
      super.new(name);
    endfunction
  endclass
  
  // NMCU operation transaction
  class nmcu_transaction extends uvm_sequence_item;
    rand operation_type_e op_type;
    rand bit [ADDR_WIDTH-1:0] src_addr_a;
    rand bit [ADDR_WIDTH-1:0] src_addr_b;
    rand bit [ADDR_WIDTH-1:0] dest_addr;
    rand int matrix_dim;
    bit operation_complete;
    bit [31:0] cycle_count;
    
    `uvm_object_utils_begin(nmcu_transaction)
      `uvm_field_enum(operation_type_e, op_type, UVM_ALL_ON)
      `uvm_field_int(src_addr_a, UVM_ALL_ON)
      `uvm_field_int(src_addr_b, UVM_ALL_ON)
      `uvm_field_int(dest_addr, UVM_ALL_ON)
      `uvm_field_int(matrix_dim, UVM_ALL_ON)
    `uvm_object_utils_end
    
    constraint matrix_dim_c { matrix_dim inside {[4:128]}; matrix_dim % 4 == 0; }
    
    function new(string name = "nmcu_transaction");
      super.new(name);
    endfunction
  endclass
  
  //=============================================================================
  // Sequence Classes
  //=============================================================================
  
  // Basic memory read/write sequence
  class mem_basic_seq extends uvm_sequence #(mem_transaction);
    `uvm_object_utils(mem_basic_seq)
    
    function new(string name = "mem_basic_seq");
      super.new(name);
    endfunction
    
    virtual task body();
      mem_transaction req;
      
      repeat(10) begin
        req = mem_transaction::type_id::create("req");
        start_item(req);
        assert(req.randomize());
        finish_item(req);
      end
    endtask
  endclass
  
  // Matrix multiplication sequence
  class matrix_mul_seq extends uvm_sequence #(nmcu_transaction);
    `uvm_object_utils(matrix_mul_seq)
    
    rand int num_operations;
    constraint num_ops_c { num_operations inside {[1:5]}; }
    
    function new(string name = "matrix_mul_seq");
      super.new(name);
    endfunction
    
    virtual task body();
      nmcu_transaction req;
      
      repeat(num_operations) begin
        req = nmcu_transaction::type_id::create("req");
        start_item(req);
        assert(req.randomize() with { op_type == MATRIX_MUL; });
        finish_item(req);
        
        // Wait for operation completion
        wait(req.operation_complete);
        `uvm_info("MATRIX_SEQ", $sformatf("Matrix multiplication completed in %0d cycles", req.cycle_count), UVM_MEDIUM)
      end
    endtask
  endclass
  
  // UCIe communication sequence
  class ucie_comm_seq extends uvm_sequence #(ucie_transaction);
    `uvm_object_utils(ucie_comm_seq)
    
    rand int num_transfers;
    constraint transfers_c { num_transfers inside {[10:50]}; }
    
    function new(string name = "ucie_comm_seq");
      super.new(name);
    endfunction
    
    virtual task body();
      ucie_transaction req;
      
      repeat(num_transfers) begin
        req = ucie_transaction::type_id::create("req");
        start_item(req);
        assert(req.randomize());
        finish_item(req);
      end
    endtask
  endclass
  
  //=============================================================================
  // Driver Classes
  //=============================================================================
  
  // Memory interface driver
  class mem_driver extends uvm_driver #(mem_transaction);
    `uvm_component_utils(mem_driver)
    
    virtual mem_interface vif;
    
    function new(string name = "mem_driver", uvm_component parent = null);
      super.new(name, parent);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      if (!uvm_config_db#(virtual mem_interface)::get(this, "", "vif", vif))
        `uvm_fatal("DRIVER", "Could not get memory interface")
    endfunction
    
    virtual task run_phase(uvm_phase phase);
      mem_transaction req;
      
      forever begin
        seq_item_port.get_next_item(req);
        drive_transaction(req);
        seq_item_port.item_done();
      end
    endtask
    
    virtual task drive_transaction(mem_transaction req);
      @(posedge vif.clk);
      
      case (req.req_type)
        READ_REQ: begin
          vif.mem_addr <= req.addr;
          vif.mem_read <= 1'b1;
          vif.mem_write <= 1'b0;
          wait(vif.mem_ready);
          req.response_data = vif.mem_rdata;
          req.response_valid = 1'b1;
        end
        
        WRITE_REQ: begin
          vif.mem_addr <= req.addr;
          vif.mem_wdata <= req.data;
          vif.mem_write <= 1'b1;
          vif.mem_read <= 1'b0;
          wait(vif.mem_ready);
        end
      endcase
      
      @(posedge vif.clk);
      vif.mem_read <= 1'b0;
      vif.mem_write <= 1'b0;
    endtask
  endclass
  
  // UCIe driver
  class ucie_driver extends uvm_driver #(ucie_transaction);
    `uvm_component_utils(ucie_driver)
    
    virtual ucie_interface vif;
    
    function new(string name = "ucie_driver", uvm_component parent = null);
      super.new(name, parent);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      if (!uvm_config_db#(virtual ucie_interface)::get(this, "", "vif", vif))
        `uvm_fatal("DRIVER", "Could not get UCIe interface")
    endfunction
    
    virtual task run_phase(uvm_phase phase);
      ucie_transaction req;
      
      forever begin
        seq_item_port.get_next_item(req);
        drive_ucie_transaction(req);
        seq_item_port.item_done();
      end
    endtask
    
    virtual task drive_ucie_transaction(ucie_transaction req);
      @(posedge vif.clk);
      
      vif.tx_valid <= req.valid;
      vif.tx_data <= req.payload;
      vif.tx_dest <= req.dest_addr;
      vif.tx_channel <= req.channel_id;
      
      wait(vif.tx_ready);
      req.ready = vif.tx_ready;
      
      @(posedge vif.clk);
      vif.tx_valid <= 1'b0;
    endtask
  endclass
  
  //=============================================================================
  // Monitor Classes
  //=============================================================================
  
  // Memory monitor
  class mem_monitor extends uvm_monitor;
    `uvm_component_utils(mem_monitor)
    
    virtual mem_interface vif;
    uvm_analysis_port #(mem_transaction) ap;
    
    function new(string name = "mem_monitor", uvm_component parent = null);
      super.new(name, parent);
      ap = new("ap", this);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      if (!uvm_config_db#(virtual mem_interface)::get(this, "", "vif", vif))
        `uvm_fatal("MONITOR", "Could not get memory interface")
    endfunction
    
    virtual task run_phase(uvm_phase phase);
      mem_transaction trans;
      
      forever begin
        @(posedge vif.clk);
        
        if (vif.mem_read || vif.mem_write) begin
          trans = mem_transaction::type_id::create("trans");
          trans.addr = vif.mem_addr;
          
          if (vif.mem_write) begin
            trans.req_type = WRITE_REQ;
            trans.data = vif.mem_wdata;
          end else begin
            trans.req_type = READ_REQ;
            wait(vif.mem_ready);
            trans.response_data = vif.mem_rdata;
          end
          
          ap.write(trans);
        end
      end
    endtask
  endclass
  
  // Performance monitor
  class performance_monitor extends uvm_monitor;
    `uvm_component_utils(performance_monitor)
    
    virtual nmcu_interface vif;
    
    // Performance counters
    int total_operations;
    int total_cycles;
    real avg_latency;
    real throughput;
    
    function new(string name = "performance_monitor", uvm_component parent = null);
      super.new(name, parent);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      if (!uvm_config_db#(virtual nmcu_interface)::get(this, "", "vif", vif))
        `uvm_fatal("PERF_MON", "Could not get NMCU interface")
    endfunction
    
    virtual task run_phase(uvm_phase phase);
      int start_cycle, end_cycle;
      
      forever begin
        @(posedge vif.operation_start);
        start_cycle = $time;
        total_operations++;
        
        @(posedge vif.operation_complete);
        end_cycle = $time;
        total_cycles += (end_cycle - start_cycle);
        
        // Calculate metrics
        avg_latency = real'(total_cycles) / real'(total_operations);
        throughput = real'(total_operations) / real'($time);
        
        `uvm_info("PERF", $sformatf("Operation %0d completed, Avg Latency: %0.2f, Throughput: %0.2f ops/cycle", 
                  total_operations, avg_latency, throughput), UVM_MEDIUM)
      end
    endtask
    
    virtual function void report_phase(uvm_phase phase);
      `uvm_info("PERF_REPORT", $sformatf("Final Performance Metrics:"), UVM_LOW)
      `uvm_info("PERF_REPORT", $sformatf("Total Operations: %0d", total_operations), UVM_LOW)
      `uvm_info("PERF_REPORT", $sformatf("Average Latency: %0.2f cycles", avg_latency), UVM_LOW)
      `uvm_info("PERF_REPORT", $sformatf("Throughput: %0.2f ops/cycle", throughput), UVM_LOW)
    endfunction
  endclass
  
  //=============================================================================
  // Scoreboard
  //=============================================================================
  
  class nmcu_scoreboard extends uvm_scoreboard;
    `uvm_component_utils(nmcu_scoreboard)
    
    uvm_analysis_imp_mem #(mem_transaction, nmcu_scoreboard) mem_imp;
    uvm_analysis_imp_ucie #(ucie_transaction, nmcu_scoreboard) ucie_imp;
    
    // Expected vs Actual queues
    mem_transaction mem_expected_q[$];
    mem_transaction mem_actual_q[$];
    
    int matches;
    int mismatches;
    
    function new(string name = "nmcu_scoreboard", uvm_component parent = null);
      super.new(name, parent);
      mem_imp = new("mem_imp", this);
      ucie_imp = new("ucie_imp", this);
    endfunction
    
    virtual function void write_mem(mem_transaction trans);
      mem_actual_q.push_back(trans);
      check_mem_transaction();
    endfunction
    
    virtual function void write_ucie(ucie_transaction trans);
      // UCIe transaction checking logic
      `uvm_info("SCOREBOARD", $sformatf("UCIe transaction on channel %0d", trans.channel_id), UVM_HIGH)
    endfunction
    
    virtual function void check_mem_transaction();
      mem_transaction expected, actual;
      
      if (mem_expected_q.size() > 0 && mem_actual_q.size() > 0) begin
        expected = mem_expected_q.pop_front();
        actual = mem_actual_q.pop_front();
        
        if (expected.compare(actual)) begin
          matches++;
          `uvm_info("SCOREBOARD", "Memory transaction MATCH", UVM_HIGH)
        end else begin
          mismatches++;
          `uvm_error("SCOREBOARD", "Memory transaction MISMATCH")
        end
      end
    endfunction
    
    virtual function void report_phase(uvm_phase phase);
      `uvm_info("SCOREBOARD", $sformatf("Matches: %0d, Mismatches: %0d", matches, mismatches), UVM_LOW)
    endfunction
  endclass
  
  //=============================================================================
  // Agent Classes
  //=============================================================================
  
  class mem_agent extends uvm_agent;
    `uvm_component_utils(mem_agent)
    
    mem_driver driver;
    mem_monitor monitor;
    uvm_sequencer #(mem_transaction) sequencer;
    
    function new(string name = "mem_agent", uvm_component parent = null);
      super.new(name, parent);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      
      if (is_active == UVM_ACTIVE) begin
        driver = mem_driver::type_id::create("driver", this);
        sequencer = uvm_sequencer#(mem_transaction)::type_id::create("sequencer", this);
      end
      
      monitor = mem_monitor::type_id::create("monitor", this);
    endfunction
    
    virtual function void connect_phase(uvm_phase phase);
      if (is_active == UVM_ACTIVE) begin
        driver.seq_item_port.connect(sequencer.seq_item_export);
      end
    endfunction
  endclass
  
  class ucie_agent extends uvm_agent;
    `uvm_component_utils(ucie_agent)
    
    ucie_driver driver;
    // ucie_monitor monitor;
    uvm_sequencer #(ucie_transaction) sequencer;
    
    function new(string name = "ucie_agent", uvm_component parent = null);
      super.new(name, parent);
    endfunction
    
    virtual function void build_phase(uvm_phase phase);
      super.build_phase(phase);
      
      if (is_active == UVM_ACTIVE) begin
        driver = ucie_driver::type_id::create("driver", this);
        sequencer = uvm_sequencer#(ucie_transaction)::type_id::create("sequencer", this);
      end
    endfunction
    
    virtual function void connect_phase(uvm_phase phase);
      if (is_active == UVM_ACTIVE) begin
        driver.seq_item_port.connect(sequencer.seq_item_export);
      end
    endfunction
  endclass
  
endpackage