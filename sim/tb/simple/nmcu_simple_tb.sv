// nmcu_simple_tb.sv
// Simple Verilator-compatible testbench for NMCU Chiplet
// No UVM dependencies - pure SystemVerilog

`timescale 1ns/1ps

`include "parameters.sv"
`include "types.sv"

module nmcu_simple_tb;

    import nmcu_pkg::*;
    import nmcu_types::*;

    // Clock and reset
    logic main_clk;
    logic nmcu_clk;
    logic rst_n;
    
    // Clock generation
    initial begin
        main_clk = 0;
        forever #2.5ns main_clk = ~main_clk;  // 200MHz
    end
    
    initial begin
        nmcu_clk = 0;
        forever #5ns nmcu_clk = ~nmcu_clk;    // 100MHz
    end
    
    // Reset generation
    initial begin
        rst_n = 0;
        #100ns;
        rst_n = 1;
        $display("[TB] Reset released at time %0t", $time);
    end
    
    // Interface instantiations
    ucie_interface ucie_if(main_clk, rst_n);
    mem_interface mem_if(main_clk, rst_n);
    control_interface ctrl_if(main_clk, rst_n);
    pe_if pe_if_inst(nmcu_clk, rst_n);
    cache_interface cache_if(nmcu_clk, rst_n);
    
    // DUT instantiation
    nmcu_chiplet dut (
        .clk(main_clk),
        .rst_n(rst_n),
        .ucie_if(ucie_if.slave),
        .mem_if(mem_if.master),
        .status_reg(),
        .ready(),
        .error()
    );
    
    // UCIe IP Model
    ucie_ip_model #(
        .LANES(UCIE_LANES),
        .DATA_WIDTH(UCIE_FLIT_WIDTH)
    ) ucie_model (
        .tx_clk(ucie_if.tx_clk),
        .rx_clk(ucie_if.rx_clk),
        .rst_n(rst_n),
        .tx_data(ucie_if.tx_data),
        .tx_valid(ucie_if.tx_valid),
        .tx_ready(ucie_if.tx_ready),
        .rx_data(ucie_if.rx_data),
        .rx_valid(ucie_if.rx_valid),
        .rx_ready(ucie_if.rx_ready)
    );
    
    // Memory Model
    simple_memory_model #(
        .MEMORY_SIZE(32'h1000_0000),  // 256MB
        .LATENCY_CYCLES(10)
    ) mem_model (
        .mem_if(mem_if.slave)
    );
    
    // Test stimulus
    initial begin
        $display("[TB] Starting NMCU Chiplet simulation");
        $display("[TB] Main clock: 200MHz, NMCU clock: 100MHz");
        
        // Wait for reset
        wait(rst_n);
        repeat(10) @(posedge main_clk);
        
        // Initialize interfaces
        initialize_interfaces();
        
        // Run basic tests
        run_basic_tests();
        
        // Finish simulation
        repeat(100) @(posedge main_clk);
        $display("[TB] Simulation completed at time %0t", $time);
        $finish;
    end
    
    // Initialize all interfaces to safe values
    task initialize_interfaces();
        $display("[TB] Initializing interfaces...");
        
        // UCIe interface initialization
        ucie_if.link_up = 1'b1;
        ucie_if.link_training = 1'b0;
        ucie_if.link_status = 8'h01;
        ucie_if.tx_credits = 8'hFF;
        ucie_if.credit_update = 1'b0;
        
        // Control interface initialization
        ctrl_if.cmd_valid = 1'b0;
        ctrl_if.cmd_opcode = 8'h00;
        ctrl_if.cmd_addr = '0;
        ctrl_if.cmd_data = '0;
        ctrl_if.cmd_length = '0;
        ctrl_if.cmd_config = '0;
        ctrl_if.cmd_id = '0;
        ctrl_if.resp_ready = 1'b1;
        ctrl_if.ctrl_enable = 1'b1;
        ctrl_if.ctrl_reset = 1'b0;
        ctrl_if.event_mask = 8'hFF;
        
        // PE interface initialization
        pe_if.pe_enable = 1'b0;
        pe_if.pe_start = 1'b0;
        pe_if.pe_config = 8'h00;
        pe_if.pe_x_enable = '0;
        pe_if.pe_y_enable = '0;
        pe_if.pe_operation = 3'b000;
        pe_if.input_valid = 1'b0;
        pe_if.input_data = '0;
        pe_if.input_x_addr = '0;
        pe_if.input_y_addr = '0;
        pe_if.input_broadcast = 1'b0;
        pe_if.weight_valid = 1'b0;
        pe_if.weight_data = '0;
        pe_if.weight_x_addr = '0;
        pe_if.weight_y_addr = '0;
        pe_if.weight_load_en = 1'b0;
        pe_if.result_ready = 1'b1;
        pe_if.partial_sum_clear = 1'b0;
        
        // Cache interface initialization
        cache_if.req_valid = 1'b0;
        cache_if.req_type = 3'b000;
        cache_if.req_addr = '0;
        cache_if.req_data = '0;
        cache_if.req_id = '0;
        cache_if.req_be = '0;
        cache_if.resp_ready = 1'b1;
        cache_if.cache_enable = 1'b1;
        cache_if.cache_flush = 1'b0;
        cache_if.cache_invalidate = 1'b0;
        cache_if.prefetch_req = 1'b0;
        cache_if.prefetch_addr = '0;
        cache_if.prefetch_type = 2'b00;
        
        @(posedge main_clk);
        $display("[TB] Interface initialization complete");
    endtask
    
    // Run basic functionality tests
    task run_basic_tests();
        $display("[TB] Running basic tests...");
        
        // Test 1: Basic control command
        test_control_command();
        
        // Test 2: Memory access
        test_memory_access();
        
        // Test 3: PE operation
        test_pe_operation();
        
        $display("[TB] Basic tests completed");
    endtask
    
    // Test control command interface
    task test_control_command();
        $display("[TB] Testing control command interface...");
        
        @(posedge main_clk);
        ctrl_if.cmd_valid = 1'b1;
        ctrl_if.cmd_opcode = 8'h07; // CMD_STATUS_READ
        ctrl_if.cmd_addr = 32'h0000_0000;
        ctrl_if.cmd_id = 4'h1;
        
        wait(ctrl_if.cmd_ready);
        @(posedge main_clk);
        ctrl_if.cmd_valid = 1'b0;
        
        // Wait for response
        wait(ctrl_if.resp_valid);
        @(posedge main_clk);
        
        $display("[TB] Control command test completed");
    endtask
    
    // Test memory access
    task test_memory_access();
        $display("[TB] Testing memory access...");
        
        // This would be handled by the cache system
        // For now, just verify the interface is connected
        
        $display("[TB] Memory access test completed");
    endtask
    
    // Test PE operation
    task test_pe_operation();
        $display("[TB] Testing PE operation...");
        
        @(posedge nmcu_clk);
        pe_if.pe_enable = 1'b1;
        pe_if.pe_start = 1'b1;
        pe_if.pe_operation = 3'b001; // PE_MAC
        
        @(posedge nmcu_clk);
        pe_if.pe_start = 1'b0;
        
        // Wait for PE to be ready
        wait(pe_if.pe_ready);
        
        $display("[TB] PE operation test completed");
    endtask
    
    // Waveform dumping for Verilator
    initial begin
        if ($test$plusargs("trace")) begin
            $dumpfile("nmcu_simple_tb.vcd");
            $dumpvars(0, nmcu_simple_tb);
            $display("[TB] VCD tracing enabled");
        end
    end
    
    // Simulation timeout
    initial begin
        #10ms;
        $display("[TB] ERROR: Simulation timeout!");
        $finish;
    end
    
    // Monitor key signals
    always @(posedge main_clk) begin
        if (rst_n && dut.ready) begin
            $display("[TB] DUT is ready at time %0t", $time);
        end
        
        if (rst_n && dut.error) begin
            $display("[TB] ERROR: DUT error signal asserted at time %0t", $time);
        end
    end

endmodule
