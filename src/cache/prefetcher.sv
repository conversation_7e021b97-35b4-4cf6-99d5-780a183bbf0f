// =============================================================================
// File: prefetcher.sv  
// Description: Hardware Prefetcher for NMCU Cache System
// Author: Generated for NMCU Chiplet Design Competition
// =============================================================================

`include "parameters.sv"
`include "types.sv"

module prefetcher #(
    parameter int ADDR_WIDTH = `ADDR_WIDTH,
    parameter int DATA_WIDTH = `DATA_WIDTH,
    parameter int CACHE_LINE_SIZE = `CACHE_LINE_SIZE,
    parameter int PREFETCH_QUEUE_DEPTH = 8,
    parameter int STRIDE_TABLE_ENTRIES = 16,
    parameter int STREAM_BUFFER_ENTRIES = 4,
    parameter int MAX_PREFETCH_DISTANCE = 4
) (
    input  logic                    clk,
    input  logic                    rst_n,
    
    // Cache Miss Interface (训练输入)
    input  logic                    miss_valid,
    input  logic [ADDR_WIDTH-1:0]   miss_addr,
    input  logic                    miss_ready,
    
    // Cache Hit Interface (可选，用于更精确的预测)
    input  logic                    hit_valid,
    input  logic [ADDR_WIDTH-1:0]   hit_addr,
    
    // Prefetch Request Output to Memory Controller
    output logic                    prefetch_req_valid,
    output logic [ADDR_WIDTH-1:0]   prefetch_req_addr,
    input  logic                    prefetch_req_ready,
    
    // Prefetch Response from Memory Controller
    input  logic                    prefetch_resp_valid,
    input  logic [ADDR_WIDTH-1:0]   prefetch_resp_addr,
    input  logic [DATA_WIDTH-1:0]   prefetch_resp_data,
    output logic                    prefetch_resp_ready,
    
    // Control Interface
    input  logic                    prefetch_enable,
    input  logic [2:0]             prefetch_aggressiveness, // 0-7, 预取激进程度
    
    // Status/Debug
    output logic [31:0]            prefetch_hits,
    output logic [31:0]            prefetch_requests,
    output logic [31:0]            prefetch_useful
);

    // =========================================================================
    // Internal Types and Parameters
    // =========================================================================
    
    typedef struct packed {
        logic valid;
        logic [ADDR_WIDTH-1:0] pc;              // 指令地址(可选)
        logic [ADDR_WIDTH-1:0] last_addr;       // 上次访问地址
        logic signed [15:0]    stride;          // 步长
        logic [3:0]            confidence;      // 置信度
        logic [3:0]            access_count;    // 访问次数
    } stride_entry_t;
    
    typedef struct packed {
        logic valid;
        logic [ADDR_WIDTH-1:0] base_addr;       // 流的基地址
        logic [ADDR_WIDTH-1:0] next_addr;       // 下一个预期地址
        logic [3:0]            remaining;       // 剩余预取数量
        logic [2:0]            direction;       // 方向 (0=forward, 1=backward)
    } stream_entry_t;
    
    typedef struct packed {
        logic valid;
        logic [ADDR_WIDTH-1:0] addr;
        logic issued;                           // 是否已发送到内存
        logic [3:0]            timestamp;       // 用于LRU替换
    } prefetch_queue_entry_t;

    // =========================================================================
    // Internal Signals
    // =========================================================================
    
    // Stride Predictor
    stride_entry_t stride_table [STRIDE_TABLE_ENTRIES-1:0];
    logic [$clog2(STRIDE_TABLE_ENTRIES)-1:0] stride_alloc_ptr;
    logic [$clog2(STRIDE_TABLE_ENTRIES)-1:0] stride_hit_idx;
    logic stride_hit;
    logic signed [15:0] detected_stride;
    
    // Stream Buffer
    stream_entry_t stream_buffer [STREAM_BUFFER_ENTRIES-1:0];
    logic [$clog2(STREAM_BUFFER_ENTRIES)-1:0] stream_alloc_ptr;
    logic [$clog2(STREAM_BUFFER_ENTRIES)-1:0] stream_hit_idx;
    logic stream_hit;
    
    // Prefetch Queue
    prefetch_queue_entry_t prefetch_queue [PREFETCH_QUEUE_DEPTH-1:0];
    logic [$clog2(PREFETCH_QUEUE_DEPTH)-1:0] pq_head, pq_tail;
    logic pq_full, pq_empty;
    
    // Address History (简单的最近访问历史)
    logic [ADDR_WIDTH-1:0] addr_history [3:0];
    logic [1:0] history_ptr;
    
    // Control Logic
    logic [3:0] global_timestamp;
    logic [ADDR_WIDTH-1:0] current_miss_addr;
    logic process_miss;
    
    // Statistics
    logic [31:0] stat_prefetch_hits_reg;
    logic [31:0] stat_prefetch_requests_reg;
    logic [31:0] stat_prefetch_useful_reg;

    // =========================================================================
    // Address History Management
    // =========================================================================
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            addr_history <= '{default: '0};
            history_ptr <= '0;
        end else if (miss_valid && miss_ready) begin
            addr_history[history_ptr] <= miss_addr;
            history_ptr <= history_ptr + 1;
        end
    end

    // =========================================================================
    // Stride Predictor Logic
    // =========================================================================
    
    // Stride Table Lookup
    always_comb begin
        stride_hit = 1'b0;
        stride_hit_idx = '0;
        detected_stride = '0;
        
        for (int i = 0; i < STRIDE_TABLE_ENTRIES; i++) begin
            if (stride_table[i].valid && 
                (stride_table[i].last_addr == addr_history[history_ptr-1])) begin
                stride_hit = 1'b1;
                stride_hit_idx = i;
                detected_stride = stride_table[i].stride;
                break;
            end
        end
    end
    
    // Stride Table Update
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            stride_table <= '{default: '0};
            stride_alloc_ptr <= '0;
        end else if (miss_valid && miss_ready && prefetch_enable) begin
            if (stride_hit) begin
                // Update existing entry
                stride_table[stride_hit_idx].last_addr <= miss_addr;
                if (stride_table[stride_hit_idx].stride == 
                    (miss_addr - stride_table[stride_hit_idx].last_addr)) begin
                    // Stride confirmed, increase confidence
                    if (stride_table[stride_hit_idx].confidence < 4'hF)
                        stride_table[stride_hit_idx].confidence <= 
                            stride_table[stride_hit_idx].confidence + 1;
                end else begin
                    // Stride changed, update and reset confidence
                    stride_table[stride_hit_idx].stride <= 
                        miss_addr - stride_table[stride_hit_idx].last_addr;
                    stride_table[stride_hit_idx].confidence <= 4'h1;
                end
                stride_table[stride_hit_idx].access_count <= 
                    stride_table[stride_hit_idx].access_count + 1;
            end else begin
                // Allocate new entry
                stride_table[stride_alloc_ptr].valid <= 1'b1;
                stride_table[stride_alloc_ptr].last_addr <= miss_addr;
                if (history_ptr > 0) begin
                    stride_table[stride_alloc_ptr].stride <= 
                        miss_addr - addr_history[history_ptr-1];
                end else begin
                    stride_table[stride_alloc_ptr].stride <= CACHE_LINE_SIZE;
                end
                stride_table[stride_alloc_ptr].confidence <= 4'h1;
                stride_table[stride_alloc_ptr].access_count <= 4'h1;
                stride_alloc_ptr <= stride_alloc_ptr + 1;
            end
        end
    end

    // =========================================================================
    // Stream Buffer Logic
    // =========================================================================
    
    // Stream Buffer Lookup
    always_comb begin
        stream_hit = 1'b0;
        stream_hit_idx = '0;
        
        for (int i = 0; i < STREAM_BUFFER_ENTRIES; i++) begin
            if (stream_buffer[i].valid && 
                (stream_buffer[i].next_addr == miss_addr ||
                 (miss_addr >= stream_buffer[i].base_addr && 
                  miss_addr < stream_buffer[i].base_addr + (CACHE_LINE_SIZE * 8)))) begin
                stream_hit = 1'b1;
                stream_hit_idx = i;
                break;
            end
        end
    end
    
    // Stream Buffer Update
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            stream_buffer <= '{default: '0};
            stream_alloc_ptr <= '0;
        end else if (miss_valid && miss_ready && prefetch_enable) begin
            if (stream_hit) begin
                // Update existing stream
                stream_buffer[stream_hit_idx].next_addr <= 
                    miss_addr + CACHE_LINE_SIZE;
                if (stream_buffer[stream_hit_idx].remaining > 0) begin
                    stream_buffer[stream_hit_idx].remaining <= 
                        stream_buffer[stream_hit_idx].remaining - 1;
                end
            end else if (stride_hit && stride_table[stride_hit_idx].confidence > 4'h4) begin
                // Create new stream based on stride prediction
                stream_buffer[stream_alloc_ptr].valid <= 1'b1;
                stream_buffer[stream_alloc_ptr].base_addr <= miss_addr;
                stream_buffer[stream_alloc_ptr].next_addr <= 
                    miss_addr + detected_stride;
                stream_buffer[stream_alloc_ptr].remaining <= 
                    MAX_PREFETCH_DISTANCE;
                stream_buffer[stream_alloc_ptr].direction <= 
                    (detected_stride > 0) ? 3'b000 : 3'b001;
                stream_alloc_ptr <= stream_alloc_ptr + 1;
            end
        end
    end

    // =========================================================================
    // Prefetch Queue Management
    // =========================================================================
    
    assign pq_full = ((pq_tail + 1) % PREFETCH_QUEUE_DEPTH) == pq_head;
    assign pq_empty = (pq_head == pq_tail);
    
    // Prefetch Queue Allocation
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            prefetch_queue <= '{default: '0};
            pq_head <= '0;
            pq_tail <= '0;
            process_miss <= 1'b0;
            current_miss_addr <= '0;
        end else begin
            // Capture miss for processing
            if (miss_valid && miss_ready) begin
                process_miss <= 1'b1;
                current_miss_addr <= miss_addr;
            end else if (process_miss) begin
                process_miss <= 1'b0;
            end
            
            // Generate prefetch requests based on predictions
            if (process_miss && prefetch_enable && !pq_full) begin
                logic [ADDR_WIDTH-1:0] prefetch_addr;
                logic should_prefetch;
                
                should_prefetch = 1'b0;
                prefetch_addr = '0;
                
                // Strategy 1: Stride-based prefetching
                if (stride_hit && 
                    stride_table[stride_hit_idx].confidence > prefetch_aggressiveness) begin
                    prefetch_addr = current_miss_addr + detected_stride;
                    should_prefetch = 1'b1;
                end
                // Strategy 2: Stream-based prefetching
                else if (stream_hit && stream_buffer[stream_hit_idx].remaining > 0) begin
                    prefetch_addr = stream_buffer[stream_hit_idx].next_addr;
                    should_prefetch = 1'b1;
                end
                // Strategy 3: Simple next-line prefetching (fallback)
                else if (prefetch_aggressiveness > 3'h2) begin
                    prefetch_addr = current_miss_addr + CACHE_LINE_SIZE;
                    should_prefetch = 1'b1;
                end
                
                if (should_prefetch) begin
                    // Check if address is already in queue
                    logic addr_exists;
                    addr_exists = 1'b0;
                    for (int i = 0; i < PREFETCH_QUEUE_DEPTH; i++) begin
                        if (prefetch_queue[i].valid && 
                            prefetch_queue[i].addr == prefetch_addr) begin
                            addr_exists = 1'b1;
                            break;
                        end
                    end
                    
                    if (!addr_exists) begin
                        prefetch_queue[pq_tail].valid <= 1'b1;
                        prefetch_queue[pq_tail].addr <= prefetch_addr;
                        prefetch_queue[pq_tail].issued <= 1'b0;
                        prefetch_queue[pq_tail].timestamp <= global_timestamp;
                        pq_tail <= (pq_tail + 1) % PREFETCH_QUEUE_DEPTH;
                    end
                end
            end
            
            // Issue prefetch requests
            if (!pq_empty && prefetch_queue[pq_head].valid && 
                !prefetch_queue[pq_head].issued && prefetch_req_ready) begin
                prefetch_queue[pq_head].issued <= 1'b1;
            end
            
            // Handle prefetch responses and queue removal
            if (prefetch_resp_valid && prefetch_resp_ready) begin
                for (int i = 0; i < PREFETCH_QUEUE_DEPTH; i++) begin
                    if (prefetch_queue[i].valid && 
                        prefetch_queue[i].addr == prefetch_resp_addr) begin
                        prefetch_queue[i].valid <= 1'b0;
                        if (i == pq_head) begin
                            pq_head <= (pq_head + 1) % PREFETCH_QUEUE_DEPTH;
                        end
                        break;
                    end
                end
            end
        end
    end

    // =========================================================================
    // Output Logic
    // =========================================================================
    
    assign prefetch_req_valid = !pq_empty && 
                               prefetch_queue[pq_head].valid && 
                               !prefetch_queue[pq_head].issued;
                               
    assign prefetch_req_addr = prefetch_queue[pq_head].addr;
    assign prefetch_resp_ready = 1'b1; // Always ready to accept responses

    // =========================================================================
    // Global Timestamp and Statistics
    // =========================================================================
    
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            global_timestamp <= '0;
            stat_prefetch_hits_reg <= '0;
            stat_prefetch_requests_reg <= '0;
            stat_prefetch_useful_reg <= '0;
        end else begin
            global_timestamp <= global_timestamp + 1;
            
            // Count prefetch requests
            if (prefetch_req_valid && prefetch_req_ready) begin
                stat_prefetch_requests_reg <= stat_prefetch_requests_reg + 1;
            end
            
            // Count prefetch hits (simplified - when we get a hit on prefetched data)
            if (hit_valid) begin
                for (int i = 0; i < PREFETCH_QUEUE_DEPTH; i++) begin
                    if (prefetch_queue[i].valid && 
                        prefetch_queue[i].addr == hit_addr && 
                        prefetch_queue[i].issued) begin
                        stat_prefetch_hits_reg <= stat_prefetch_hits_reg + 1;
                        stat_prefetch_useful_reg <= stat_prefetch_useful_reg + 1;
                        break;
                    end
                end
            end
        end
    end
    
    assign prefetch_hits = stat_prefetch_hits_reg;
    assign prefetch_requests = stat_prefetch_requests_reg;
    assign prefetch_useful = stat_prefetch_useful_reg;

    // =========================================================================
    // Assertions for Debug and Verification
    // =========================================================================
    
    `ifdef SIMULATION
    // Check that we don't overflow the prefetch queue
    assert property (@(posedge clk) disable iff (!rst_n)
        prefetch_req_valid |-> !pq_full)
        else $error("Prefetch request issued when queue is full");
    
    // Check that addresses are cache-line aligned
    assert property (@(posedge clk) disable iff (!rst_n)
        prefetch_req_valid |-> (prefetch_req_addr % CACHE_LINE_SIZE == 0))
        else $warning("Prefetch address not cache-line aligned");
    
    // Performance hint: High confidence strides should generate prefetches
    assert property (@(posedge clk) disable iff (!rst_n || !prefetch_enable)
        (stride_hit && stride_table[stride_hit_idx].confidence > 4'h8) 
        |-> ##[1:3] prefetch_req_valid)
        else $info("High confidence stride did not generate prefetch");
    `endif

endmodule