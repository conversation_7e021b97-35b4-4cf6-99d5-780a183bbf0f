# Makefile.verilator
#
# This Makefile compiles the nmcu_chiplet design and testbench using Verilator.
# It generates a C++ executable that simulates the SystemVerilog code.

# --- Configuration ---
VERILATOR_EXE   = verilator
TOP_MODULE      = nmcu_chiplet_tb
SIM_BUILD_DIR   = ./obj_dir
SIM_EXE_NAME    = $(TOP_MODULE)_sim
TRACE_FILE      = $(TOP_MODULE).fst
RM              = rm -rf

# --- Source Files ---
# Find all SystemVerilog files in src/ and sim/
SRCS := $(shell find src -name "*.sv")
SRCS += $(shell find sim/models -name "*.sv")
SRCS += $(shell find sim/tb -name "*.sv")

# --- Include Directories ---
# Verilator uses -y for directories to search for modules/packages/includes.
# Be comprehensive to ensure all dependencies are found.
INC_DIRS := \
	src \
	src/cache \
	src/common \
	src/control \
	src/interconnect \
	src/memory_if \
	src/pe_array \
	src/top \
	sim/models \
	sim/tb \
	sim/tb/common \
	sim/tb/interfaces \
	sim/tb/top

VERILATOR_INCLUDE_OPTS = $(foreach dir,$(INC_DIRS),-y $(dir))

# --- Verilator Flags ---
# --sv: Enable SystemVerilog features
# --cc: Generate C++ code for the simulator
# --exe: Link the generated C++ code into an executable
# --build: Automatically build the generated C++ code (runs make internally)
# --trace: Enable waveform tracing
# --trace-fst: Use FST format (more efficient than VCD)
# --timescale 1ns/1ps: Set default timescale for simulation
# -top <module>: Specify the top-level module for simulation
# -Mdir <dir>: Specify the directory for generated files
# -o <name>: Specify the name of the executable
VERILATOR_FLAGS = \
	--sv \
	--cc \
	--exe \
	--build \
	--trace \
	--trace-fst \
	--timescale 1ns/1ps \
	-top $(TOP_MODULE) \
	$(VERILATOR_INCLUDE_OPTS) \
	-Mdir $(SIM_BUILD_DIR) \
	-o $(SIM_EXE_NAME)

# --- Makefile Targets ---

.PHONY: all build run clean help

all: build run

build: $(SIM_BUILD_DIR)/$(SIM_EXE_NAME)

$(SIM_BUILD_DIR)/$(SIM_EXE_NAME): $(SRCS)
	@echo "----------------------------------------------------"
	@echo " Compiling with Verilator..."
	@echo " Top Module: $(TOP_MODULE)"
	@echo " Output Dir: $(SIM_BUILD_DIR)"
	@echo " Executable: $(SIM_EXE_NAME)"
	@echo "----------------------------------------------------"
	$(VERILATOR_EXE) $(VERILATOR_FLAGS) $(SRCS)
	@echo "Verilator compilation complete. Executable: $(SIM_BUILD_DIR)/$(SIM_EXE_NAME)"

run: build
	@echo "----------------------------------------------------"
	@echo " Running Verilator simulation..."
	@echo " Waveform will be saved to: $(TRACE_FILE)"
	@echo "----------------------------------------------------"
	$(SIM_BUILD_DIR)/$(SIM_EXE_NAME) +trace_file=$(TRACE_FILE)
	@echo "Simulation complete. Waveform saved to $(TRACE_FILE)"
	@echo "To view waveforms, run: gtkwave $(TRACE_FILE)"

clean:
	@echo "----------------------------------------------------"
	@echo " Cleaning Verilator build artifacts..."
	@echo "----------------------------------------------------"
	$(RM) $(SIM_BUILD_DIR)
	$(RM) $(TRACE_FILE)
	$(RM) $(TOP_MODULE).vcd # In case VCD was used before
	@echo "Clean complete."

help:
	@echo "Usage: make -f Makefile.verilator [target]"
	@echo ""
	@echo "Targets:"
	@echo "  all    : Builds and runs the simulation (default)."
	@echo "  build  : Compiles the SystemVerilog design and testbench."
	@echo "  run    : Executes the compiled simulation."
	@echo "  clean  : Removes all generated build files and waveforms."
	@echo "  help   : Displays this help message."
